"use client";

import React, { useState, useEffect } from 'react';
import {
  XMarkIcon,
  ShareIcon,
  LinkIcon,
  UserIcon,
  ClockIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  ClipboardIcon,
  CheckIcon
} from '@heroicons/react/24/outline';
import { ShareDto, ShareOptions, PermissionRequest, PermissionDto } from '@/api/types/interfaces';
import { FileService } from '@/api/services/fileService';
import { FolderService } from '@/api/services/folderService';

interface ShareModalProps {
  isOpen: boolean;
  onClose: () => void;
  itemId: string;
  itemType: 'file' | 'folder';
  itemName: string;
  fileService?: FileService;
  folderService?: FolderService;
}

export function ShareModal({
  isOpen,
  onClose,
  itemId,
  itemType,
  itemName,
  fileService,
  folderService
}: ShareModalProps) {
  const [shares, setShares] = useState<ShareDto[]>([]);
  const [permissions, setPermissions] = useState<PermissionDto[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [copied, setCopied] = useState<string | null>(null);
  
  // Create share form
  const [shareForm, setShareForm] = useState<ShareOptions>({
    expiresAt: '',
    maxDownloads: undefined,
    password: '',
    allowDownload: true,
    allowPreview: true
  });

  // Permission form
  const [permissionForm, setPermissionForm] = useState<PermissionRequest>({
    userEmail: '',
    permission: 'read'
  });

  const [showShareForm, setShowShareForm] = useState(false);
  const [showPermissionForm, setShowPermissionForm] = useState(false);

  useEffect(() => {
    if (isOpen) {
      loadShares();
      loadPermissions();
    }
  }, [isOpen, itemId]);

  const loadShares = async () => {
    try {
      let sharesData: ShareDto[] = [];
      if (itemType === 'file' && fileService) {
        sharesData = await fileService.getShares(itemId);
      } else if (itemType === 'folder' && folderService) {
        sharesData = await folderService.getShares(itemId);
      }
      setShares(sharesData);
    } catch (error: any) {
      console.error('Failed to load shares:', error);
    }
  };

  const loadPermissions = async () => {
    try {
      let permissionsData: PermissionDto[] = [];
      if (itemType === 'file' && fileService) {
        permissionsData = await fileService.getPermissions(itemId);
      } else if (itemType === 'folder' && folderService) {
        permissionsData = await folderService.getPermissions(itemId);
      }
      setPermissions(permissionsData);
    } catch (error: any) {
      console.error('Failed to load permissions:', error);
    }
  };

  const handleCreateShare = async () => {
    setLoading(true);
    setError(null);
    
    try {
      let newShare: ShareDto;
      if (itemType === 'file' && fileService) {
        newShare = await fileService.createShare(itemId, shareForm);
      } else if (itemType === 'folder' && folderService) {
        newShare = await folderService.createShare(itemId, shareForm);
      } else {
        throw new Error('Invalid service configuration');
      }
      
      setShares(prev => [...prev, newShare]);
      setShowShareForm(false);
      setShareForm({
        expiresAt: '',
        maxDownloads: undefined,
        password: '',
        allowDownload: true,
        allowPreview: true
      });
    } catch (error: any) {
      setError(error.message || 'Failed to create share');
    } finally {
      setLoading(false);
    }
  };

  const handleDeleteShare = async (shareId: string) => {
    if (!confirm('Are you sure you want to delete this share?')) return;
    
    try {
      if (itemType === 'file' && fileService) {
        await fileService.deleteShare(itemId, shareId);
      } else if (itemType === 'folder' && folderService) {
        await folderService.deleteShare(itemId, shareId);
      }
      
      setShares(prev => prev.filter(share => share.id !== shareId));
    } catch (error: any) {
      setError(error.message || 'Failed to delete share');
    }
  };

  const handleGrantPermission = async () => {
    setLoading(true);
    setError(null);
    
    try {
      let permissionId: string;
      if (itemType === 'file' && fileService) {
        permissionId = await fileService.grantPermission(itemId, permissionForm);
      } else if (itemType === 'folder' && folderService) {
        permissionId = await folderService.grantPermission(itemId, permissionForm);
      } else {
        throw new Error('Invalid service configuration');
      }
      
      // Reload permissions to get the full data
      await loadPermissions();
      setShowPermissionForm(false);
      setPermissionForm({
        userEmail: '',
        permission: 'read'
      });
    } catch (error: any) {
      setError(error.message || 'Failed to grant permission');
    } finally {
      setLoading(false);
    }
  };

  const handleRemovePermission = async (permissionId: string) => {
    if (!confirm('Are you sure you want to remove this permission?')) return;
    
    try {
      if (itemType === 'file' && fileService) {
        await fileService.removePermission(itemId, permissionId);
      } else if (itemType === 'folder' && folderService) {
        await folderService.removePermission(itemId, permissionId);
      }
      
      setPermissions(prev => prev.filter(perm => perm.id !== permissionId));
    } catch (error: any) {
      setError(error.message || 'Failed to remove permission');
    }
  };

  const copyToClipboard = async (text: string, shareId: string) => {
    try {
      await navigator.clipboard.writeText(text);
      setCopied(shareId);
      setTimeout(() => setCopied(null), 2000);
    } catch (error) {
      console.error('Failed to copy to clipboard:', error);
    }
  };

  const formatDate = (dateString: string) => {
    return new Intl.DateTimeFormat('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(dateString));
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center gap-3">
            <ShareIcon className="w-6 h-6 text-blue-500" />
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                Share {itemType === 'file' ? 'File' : 'Folder'}
              </h2>
              <p className="text-sm text-gray-500">{itemName}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg"
          >
            <XMarkIcon className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <p className="text-red-800 text-sm">{error}</p>
            </div>
          )}

          {/* Create Share Section */}
          <div className="border rounded-lg p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">Public Links</h3>
              <button
                onClick={() => setShowShareForm(!showShareForm)}
                className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Create Link
              </button>
            </div>

            {showShareForm && (
              <div className="bg-gray-50 rounded-lg p-4 mb-4">
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Expires At
                    </label>
                    <input
                      type="datetime-local"
                      value={shareForm.expiresAt}
                      onChange={(e) => setShareForm(prev => ({ ...prev, expiresAt: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Max Downloads
                    </label>
                    <input
                      type="number"
                      value={shareForm.maxDownloads || ''}
                      onChange={(e) => setShareForm(prev => ({ ...prev, maxDownloads: e.target.value ? parseInt(e.target.value) : undefined }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Unlimited"
                    />
                  </div>
                </div>
                
                <div className="mb-4">
                  <label className="block text-sm font-medium text-gray-700 mb-1">
                    Password (Optional)
                  </label>
                  <input
                    type="password"
                    value={shareForm.password}
                    onChange={(e) => setShareForm(prev => ({ ...prev, password: e.target.value }))}
                    className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    placeholder="Leave empty for no password"
                  />
                </div>

                <div className="flex items-center gap-4 mb-4">
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={shareForm.allowDownload}
                      onChange={(e) => setShareForm(prev => ({ ...prev, allowDownload: e.target.checked }))}
                      className="rounded border-gray-300"
                    />
                    <span className="text-sm text-gray-700">Allow Download</span>
                  </label>
                  <label className="flex items-center gap-2">
                    <input
                      type="checkbox"
                      checked={shareForm.allowPreview}
                      onChange={(e) => setShareForm(prev => ({ ...prev, allowPreview: e.target.checked }))}
                      className="rounded border-gray-300"
                    />
                    <span className="text-sm text-gray-700">Allow Preview</span>
                  </label>
                </div>

                <div className="flex gap-2">
                  <button
                    onClick={() => setShowShareForm(false)}
                    className="px-4 py-2 text-gray-600 hover:text-gray-800"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleCreateShare}
                    disabled={loading}
                    className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                  >
                    {loading ? 'Creating...' : 'Create Link'}
                  </button>
                </div>
              </div>
            )}

            {/* Existing Shares */}
            <div className="space-y-3">
              {shares.map((share) => (
                <div key={share.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-1">
                      <LinkIcon className="w-4 h-4 text-blue-500" />
                      <span className="text-sm font-medium text-gray-900">
                        Public Link
                      </span>
                      {share.password && (
                        <span className="px-2 py-1 text-xs bg-yellow-100 text-yellow-800 rounded">
                          Password Protected
                        </span>
                      )}
                    </div>
                    <p className="text-xs text-gray-500">
                      Created: {formatDate(share.createdAt)}
                      {share.expiresAt && ` • Expires: ${formatDate(share.expiresAt)}`}
                      {share.maxDownloads && ` • Max Downloads: ${share.maxDownloads}`}
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <button
                      onClick={() => copyToClipboard(share.url, share.id)}
                      className="p-2 text-gray-400 hover:text-blue-600 rounded-lg"
                      title="Copy Link"
                    >
                      {copied === share.id ? (
                        <CheckIcon className="w-4 h-4 text-green-500" />
                      ) : (
                        <ClipboardIcon className="w-4 h-4" />
                      )}
                    </button>
                    <button
                      onClick={() => handleDeleteShare(share.id)}
                      className="p-2 text-gray-400 hover:text-red-600 rounded-lg"
                      title="Delete Share"
                    >
                      <TrashIcon className="w-4 h-4" />
                    </button>
                  </div>
                </div>
              ))}
              
              {shares.length === 0 && (
                <p className="text-center text-gray-500 py-4">
                  No public links created yet
                </p>
              )}
            </div>
          </div>

          {/* User Permissions Section */}
          <div className="border rounded-lg p-4">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-medium text-gray-900">User Permissions</h3>
              <button
                onClick={() => setShowPermissionForm(!showPermissionForm)}
                className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700"
              >
                Add User
              </button>
            </div>

            {showPermissionForm && (
              <div className="bg-gray-50 rounded-lg p-4 mb-4">
                <div className="grid grid-cols-2 gap-4 mb-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      User Email
                    </label>
                    <input
                      type="email"
                      value={permissionForm.userEmail}
                      onChange={(e) => setPermissionForm(prev => ({ ...prev, userEmail: e.target.value }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="<EMAIL>"
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-1">
                      Permission Level
                    </label>
                    <select
                      value={permissionForm.permission}
                      onChange={(e) => setPermissionForm(prev => ({ ...prev, permission: e.target.value as any }))}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                    >
                      <option value="read">Read Only</option>
                      <option value="write">Read & Write</option>
                      <option value="admin">Admin</option>
                    </select>
                  </div>
                </div>

                <div className="flex gap-2">
                  <button
                    onClick={() => setShowPermissionForm(false)}
                    className="px-4 py-2 text-gray-600 hover:text-gray-800"
                  >
                    Cancel
                  </button>
                  <button
                    onClick={handleGrantPermission}
                    disabled={loading || !permissionForm.userEmail}
                    className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
                  >
                    {loading ? 'Adding...' : 'Add User'}
                  </button>
                </div>
              </div>
            )}

            {/* Existing Permissions */}
            <div className="space-y-3">
              {permissions.map((permission) => (
                <div key={permission.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-center gap-3">
                    <UserIcon className="w-5 h-5 text-gray-400" />
                    <div>
                      <p className="text-sm font-medium text-gray-900">
                        {permission.userEmail}
                      </p>
                      <p className="text-xs text-gray-500">
                        {permission.permission} access • Added {formatDate(permission.createdAt)}
                      </p>
                    </div>
                  </div>
                  <button
                    onClick={() => handleRemovePermission(permission.id)}
                    className="p-2 text-gray-400 hover:text-red-600 rounded-lg"
                    title="Remove Permission"
                  >
                    <TrashIcon className="w-4 h-4" />
                  </button>
                </div>
              ))}
              
              {permissions.length === 0 && (
                <p className="text-center text-gray-500 py-4">
                  No user permissions set
                </p>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
