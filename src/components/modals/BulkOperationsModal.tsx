"use client";

import React, { useState } from 'react';
import {
  XMarkIcon,
  TrashIcon,
  FolderIcon,
  DocumentDuplicateIcon,
  ArchiveBoxIcon,
  TagIcon,
  ShareIcon,
  ArrowDownTrayIcon
} from '@heroicons/react/24/outline';
import { FileService } from '@/api/services/fileService';
import { FolderService } from '@/api/services/folderService';

interface BulkOperationsModalProps {
  isOpen: boolean;
  onClose: () => void;
  selectedItems: {
    files: string[];
    folders: string[];
  };
  fileService: FileService;
  folderService: FolderService;
  onOperationComplete: () => void;
}

type BulkOperation = 'delete' | 'move' | 'copy' | 'archive' | 'tag' | 'share' | 'download';

export function BulkOperationsModal({
  isOpen,
  onClose,
  selectedItems,
  fileService,
  folderService,
  onOperationComplete
}: BulkOperationsModalProps) {
  const [selectedOperation, setSelectedOperation] = useState<BulkOperation | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [progress, setProgress] = useState<{ completed: number; total: number } | null>(null);

  // Operation-specific state
  const [targetFolderId, setTargetFolderId] = useState('');
  const [newTags, setNewTags] = useState('');
  const [permanent, setPermanent] = useState(false);

  const totalItems = selectedItems.files.length + selectedItems.folders.length;

  const handleBulkDelete = async () => {
    setLoading(true);
    setError(null);
    setProgress({ completed: 0, total: totalItems });

    try {
      const results = {
        files: { successful: [] as string[], failed: [] as { id: string; error: string }[] },
        folders: { successful: [] as string[], failed: [] as { id: string; error: string }[] }
      };

      // Delete files
      if (selectedItems.files.length > 0) {
        try {
          const fileResult = await fileService.bulkDelete(selectedItems.files, permanent);
          results.files = fileResult;
          setProgress(prev => prev ? { ...prev, completed: prev.completed + selectedItems.files.length } : null);
        } catch (error: any) {
          selectedItems.files.forEach(id => {
            results.files.failed.push({ id, error: error.message || 'Delete failed' });
          });
        }
      }

      // Delete folders
      if (selectedItems.folders.length > 0) {
        try {
          const folderResult = await folderService.bulkDelete(selectedItems.folders, permanent, true);
          results.folders = folderResult;
          setProgress(prev => prev ? { ...prev, completed: prev.completed + selectedItems.folders.length } : null);
        } catch (error: any) {
          selectedItems.folders.forEach(id => {
            results.folders.failed.push({ id, error: error.message || 'Delete failed' });
          });
        }
      }

      const totalFailed = results.files.failed.length + results.folders.failed.length;
      if (totalFailed > 0) {
        setError(`${totalFailed} items failed to delete. Check console for details.`);
        console.error('Bulk delete failures:', results);
      }

      onOperationComplete();
    } catch (error: any) {
      setError(error.message || 'Bulk delete operation failed');
    } finally {
      setLoading(false);
      setProgress(null);
    }
  };

  const handleBulkMove = async () => {
    if (!targetFolderId) {
      setError('Please select a target folder');
      return;
    }

    setLoading(true);
    setError(null);
    setProgress({ completed: 0, total: totalItems });

    try {
      let completed = 0;

      // Move files
      for (const fileId of selectedItems.files) {
        try {
          await fileService.move(fileId, { targetParentFolderId: targetFolderId });
          completed++;
          setProgress({ completed, total: totalItems });
        } catch (error: any) {
          console.error(`Failed to move file ${fileId}:`, error);
        }
      }

      // Move folders
      for (const folderId of selectedItems.folders) {
        try {
          await folderService.move(folderId, { targetParentFolderId: targetFolderId });
          completed++;
          setProgress({ completed, total: totalItems });
        } catch (error: any) {
          console.error(`Failed to move folder ${folderId}:`, error);
        }
      }

      onOperationComplete();
    } catch (error: any) {
      setError(error.message || 'Bulk move operation failed');
    } finally {
      setLoading(false);
      setProgress(null);
    }
  };

  const handleBulkCopy = async () => {
    if (!targetFolderId) {
      setError('Please select a target folder');
      return;
    }

    setLoading(true);
    setError(null);
    setProgress({ completed: 0, total: totalItems });

    try {
      let completed = 0;

      // Copy files
      for (const fileId of selectedItems.files) {
        try {
          await fileService.copy(fileId, { targetParentFolderId: targetFolderId });
          completed++;
          setProgress({ completed, total: totalItems });
        } catch (error: any) {
          console.error(`Failed to copy file ${fileId}:`, error);
        }
      }

      // Copy folders
      for (const folderId of selectedItems.folders) {
        try {
          await folderService.copy(folderId, targetFolderId);
          completed++;
          setProgress({ completed, total: totalItems });
        } catch (error: any) {
          console.error(`Failed to copy folder ${folderId}:`, error);
        }
      }

      onOperationComplete();
    } catch (error: any) {
      setError(error.message || 'Bulk copy operation failed');
    } finally {
      setLoading(false);
      setProgress(null);
    }
  };

  const handleBulkArchive = async () => {
    setLoading(true);
    setError(null);
    setProgress({ completed: 0, total: totalItems });

    try {
      let completed = 0;

      // Archive files
      for (const fileId of selectedItems.files) {
        try {
          await fileService.archive(fileId);
          completed++;
          setProgress({ completed, total: totalItems });
        } catch (error: any) {
          console.error(`Failed to archive file ${fileId}:`, error);
        }
      }

      // Archive folders
      for (const folderId of selectedItems.folders) {
        try {
          await folderService.archive(folderId);
          completed++;
          setProgress({ completed, total: totalItems });
        } catch (error: any) {
          console.error(`Failed to archive folder ${folderId}:`, error);
        }
      }

      onOperationComplete();
    } catch (error: any) {
      setError(error.message || 'Bulk archive operation failed');
    } finally {
      setLoading(false);
      setProgress(null);
    }
  };

  const handleBulkTag = async () => {
    if (!newTags.trim()) {
      setError('Please enter tags to add');
      return;
    }

    const tags = newTags.split(',').map(tag => tag.trim()).filter(tag => tag);
    if (tags.length === 0) {
      setError('Please enter valid tags');
      return;
    }

    setLoading(true);
    setError(null);
    setProgress({ completed: 0, total: selectedItems.files.length });

    try {
      let completed = 0;

      // Add tags to files only (folders don't support tags in this implementation)
      for (const fileId of selectedItems.files) {
        try {
          await fileService.addTags(fileId, tags);
          completed++;
          setProgress({ completed, total: selectedItems.files.length });
        } catch (error: any) {
          console.error(`Failed to tag file ${fileId}:`, error);
        }
      }

      onOperationComplete();
    } catch (error: any) {
      setError(error.message || 'Bulk tag operation failed');
    } finally {
      setLoading(false);
      setProgress(null);
    }
  };

  const executeOperation = () => {
    switch (selectedOperation) {
      case 'delete':
        return handleBulkDelete();
      case 'move':
        return handleBulkMove();
      case 'copy':
        return handleBulkCopy();
      case 'archive':
        return handleBulkArchive();
      case 'tag':
        return handleBulkTag();
      default:
        setError('Operation not implemented yet');
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div>
            <h2 className="text-xl font-semibold text-gray-900">Bulk Operations</h2>
            <p className="text-sm text-gray-500">
              {totalItems} items selected ({selectedItems.files.length} files, {selectedItems.folders.length} folders)
            </p>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg"
          >
            <XMarkIcon className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-4">
              <p className="text-red-800 text-sm">{error}</p>
            </div>
          )}

          {progress && (
            <div className="mb-4">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-gray-700">Processing...</span>
                <span className="text-sm text-gray-600">{progress.completed}/{progress.total}</span>
              </div>
              <div className="w-full bg-gray-200 rounded-full h-2">
                <div 
                  className="bg-blue-500 h-2 rounded-full transition-all"
                  style={{ width: `${(progress.completed / progress.total) * 100}%` }}
                />
              </div>
            </div>
          )}

          {!selectedOperation ? (
            /* Operation Selection */
            <div className="space-y-3">
              <h3 className="text-lg font-medium text-gray-900 mb-4">Choose an operation:</h3>
              
              <button
                onClick={() => setSelectedOperation('delete')}
                className="w-full flex items-center gap-3 p-4 text-left border border-gray-200 rounded-lg hover:bg-red-50 hover:border-red-200 transition-colors"
              >
                <TrashIcon className="w-5 h-5 text-red-500" />
                <div>
                  <p className="font-medium text-gray-900">Delete Items</p>
                  <p className="text-sm text-gray-500">Move selected items to recycle bin or delete permanently</p>
                </div>
              </button>

              <button
                onClick={() => setSelectedOperation('move')}
                className="w-full flex items-center gap-3 p-4 text-left border border-gray-200 rounded-lg hover:bg-blue-50 hover:border-blue-200 transition-colors"
              >
                <FolderIcon className="w-5 h-5 text-blue-500" />
                <div>
                  <p className="font-medium text-gray-900">Move Items</p>
                  <p className="text-sm text-gray-500">Move selected items to another folder</p>
                </div>
              </button>

              <button
                onClick={() => setSelectedOperation('copy')}
                className="w-full flex items-center gap-3 p-4 text-left border border-gray-200 rounded-lg hover:bg-green-50 hover:border-green-200 transition-colors"
              >
                <DocumentDuplicateIcon className="w-5 h-5 text-green-500" />
                <div>
                  <p className="font-medium text-gray-900">Copy Items</p>
                  <p className="text-sm text-gray-500">Create copies of selected items in another folder</p>
                </div>
              </button>

              <button
                onClick={() => setSelectedOperation('archive')}
                className="w-full flex items-center gap-3 p-4 text-left border border-gray-200 rounded-lg hover:bg-yellow-50 hover:border-yellow-200 transition-colors"
              >
                <ArchiveBoxIcon className="w-5 h-5 text-yellow-500" />
                <div>
                  <p className="font-medium text-gray-900">Archive Items</p>
                  <p className="text-sm text-gray-500">Archive selected items for long-term storage</p>
                </div>
              </button>

              {selectedItems.files.length > 0 && (
                <button
                  onClick={() => setSelectedOperation('tag')}
                  className="w-full flex items-center gap-3 p-4 text-left border border-gray-200 rounded-lg hover:bg-purple-50 hover:border-purple-200 transition-colors"
                >
                  <TagIcon className="w-5 h-5 text-purple-500" />
                  <div>
                    <p className="font-medium text-gray-900">Add Tags</p>
                    <p className="text-sm text-gray-500">Add tags to selected files ({selectedItems.files.length} files)</p>
                  </div>
                </button>
              )}
            </div>
          ) : (
            /* Operation Configuration */
            <div className="space-y-4">
              <div className="flex items-center gap-2 mb-4">
                <button
                  onClick={() => setSelectedOperation(null)}
                  className="text-blue-600 hover:text-blue-800 text-sm"
                >
                  ← Back to operations
                </button>
              </div>

              {selectedOperation === 'delete' && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Delete Items</h3>
                  <label className="flex items-center gap-2 mb-4">
                    <input
                      type="checkbox"
                      checked={permanent}
                      onChange={(e) => setPermanent(e.target.checked)}
                      className="rounded border-gray-300"
                    />
                    <span className="text-sm text-gray-700">Delete permanently (cannot be undone)</span>
                  </label>
                  <p className="text-sm text-gray-600 mb-4">
                    This will {permanent ? 'permanently delete' : 'move to recycle bin'} {totalItems} items.
                  </p>
                </div>
              )}

              {(selectedOperation === 'move' || selectedOperation === 'copy') && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">
                    {selectedOperation === 'move' ? 'Move' : 'Copy'} Items
                  </h3>
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Target Folder ID
                    </label>
                    <input
                      type="text"
                      value={targetFolderId}
                      onChange={(e) => setTargetFolderId(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="Enter folder ID or leave empty for root"
                    />
                  </div>
                </div>
              )}

              {selectedOperation === 'tag' && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Add Tags</h3>
                  <div className="mb-4">
                    <label className="block text-sm font-medium text-gray-700 mb-2">
                      Tags (comma-separated)
                    </label>
                    <input
                      type="text"
                      value={newTags}
                      onChange={(e) => setNewTags(e.target.value)}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                      placeholder="tag1, tag2, tag3"
                    />
                  </div>
                  <p className="text-sm text-gray-600 mb-4">
                    Tags will be added to {selectedItems.files.length} files. Folders don't support tags.
                  </p>
                </div>
              )}

              {selectedOperation === 'archive' && (
                <div>
                  <h3 className="text-lg font-medium text-gray-900 mb-4">Archive Items</h3>
                  <p className="text-sm text-gray-600 mb-4">
                    This will archive {totalItems} items for long-term storage.
                  </p>
                </div>
              )}
            </div>
          )}
        </div>

        {/* Actions */}
        {selectedOperation && (
          <div className="flex items-center justify-end gap-3 p-6 border-t bg-gray-50">
            <button
              onClick={() => setSelectedOperation(null)}
              className="px-4 py-2 text-gray-600 hover:text-gray-800"
              disabled={loading}
            >
              Cancel
            </button>
            <button
              onClick={executeOperation}
              disabled={loading}
              className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
            >
              {loading ? 'Processing...' : `Execute ${selectedOperation}`}
            </button>
          </div>
        )}
      </div>
    </div>
  );
}
