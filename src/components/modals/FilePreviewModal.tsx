"use client";

import React, { useState, useEffect } from 'react';
import {
  XMarkIcon,
  ArrowDownTrayIcon,
  MagnifyingGlassMinusIcon,
  MagnifyingGlassPlusIcon,
  ArrowsPointingOutIcon,
  DocumentIcon
} from '@heroicons/react/24/outline';
import { FileDto } from '@/api/types/interfaces';
import { FileService } from '@/api/services/fileService';

interface FilePreviewModalProps {
  isOpen: boolean;
  onClose: () => void;
  file: FileDto | null;
  fileService: FileService;
}

export function FilePreviewModal({
  isOpen,
  onClose,
  file,
  fileService
}: FilePreviewModalProps) {
  const [previewUrl, setPreviewUrl] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [zoom, setZoom] = useState(100);
  const [fullscreen, setFullscreen] = useState(false);

  useEffect(() => {
    if (isOpen && file) {
      loadPreview();
    } else {
      setPreviewUrl(null);
      setError(null);
      setZoom(100);
      setFullscreen(false);
    }
  }, [isOpen, file]);

  const loadPreview = async () => {
    if (!file) return;

    setLoading(true);
    setError(null);

    try {
      // Check if file type is previewable
      const extension = file.name.split('.').pop()?.toLowerCase();
      const previewableTypes = ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp', 'pdf', 'txt', 'md'];
      
      if (!previewableTypes.includes(extension || '')) {
        setError('Preview not available for this file type');
        setLoading(false);
        return;
      }

      // Get preview URL from API
      const response = await fileService.getPreview(file.id, 800, 600);
      setPreviewUrl(response.url);
    } catch (error: any) {
      console.error('Failed to load preview:', error);
      setError('Failed to load preview. The file might be too large or corrupted.');
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = async () => {
    if (!file) return;
    
    try {
      const response = await fileService.download(file.id, { presigned: true });
      
      if ('url' in response) {
        const link = document.createElement('a');
        link.href = response.url;
        link.download = file.displayName || file.name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    } catch (error: any) {
      console.error('Failed to download file:', error);
    }
  };

  const handleZoomIn = () => {
    setZoom(prev => Math.min(prev + 25, 300));
  };

  const handleZoomOut = () => {
    setZoom(prev => Math.max(prev - 25, 25));
  };

  const handleZoomReset = () => {
    setZoom(100);
  };

  const toggleFullscreen = () => {
    setFullscreen(prev => !prev);
  };

  const getFileIcon = () => {
    if (!file) return <DocumentIcon className="w-16 h-16 text-gray-400" />;
    
    const extension = file.name.split('.').pop()?.toLowerCase();
    
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'].includes(extension || '')) {
      return <img src="/icons/image.svg" alt="Image" className="w-16 h-16" />;
    }
    if (['pdf'].includes(extension || '')) {
      return <img src="/icons/pdf.svg" alt="PDF" className="w-16 h-16" />;
    }
    if (['txt', 'md'].includes(extension || '')) {
      return <img src="/icons/text.svg" alt="Text" className="w-16 h-16" />;
    }
    return <DocumentIcon className="w-16 h-16 text-gray-400" />;
  };

  const renderPreview = () => {
    if (loading) {
      return (
        <div className="flex items-center justify-center h-96">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          <span className="ml-3 text-gray-600">Loading preview...</span>
        </div>
      );
    }

    if (error) {
      return (
        <div className="flex flex-col items-center justify-center h-96 text-center">
          {getFileIcon()}
          <h3 className="mt-4 text-lg font-medium text-gray-900">{file?.displayName || file?.name}</h3>
          <p className="mt-2 text-sm text-red-600">{error}</p>
          <button
            onClick={handleDownload}
            className="mt-4 flex items-center gap-2 px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700"
          >
            <ArrowDownTrayIcon className="w-4 h-4" />
            Download File
          </button>
        </div>
      );
    }

    if (!previewUrl) {
      return (
        <div className="flex flex-col items-center justify-center h-96 text-center">
          {getFileIcon()}
          <h3 className="mt-4 text-lg font-medium text-gray-900">{file?.displayName || file?.name}</h3>
          <p className="mt-2 text-sm text-gray-500">No preview available</p>
        </div>
      );
    }

    const extension = file?.name.split('.').pop()?.toLowerCase();

    // Image preview
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'].includes(extension || '')) {
      return (
        <div className="flex items-center justify-center min-h-96 p-4">
          <img
            src={previewUrl}
            alt={file?.displayName || file?.name}
            className="max-w-full max-h-full object-contain transition-transform"
            style={{ transform: `scale(${zoom / 100})` }}
          />
        </div>
      );
    }

    // PDF preview
    if (extension === 'pdf') {
      return (
        <div className="h-96">
          <iframe
            src={previewUrl}
            className="w-full h-full border-0"
            title={file?.displayName || file?.name}
          />
        </div>
      );
    }

    // Text preview
    if (['txt', 'md'].includes(extension || '')) {
      return (
        <div className="h-96 overflow-auto">
          <iframe
            src={previewUrl}
            className="w-full h-full border-0"
            title={file?.displayName || file?.name}
          />
        </div>
      );
    }

    return (
      <div className="flex flex-col items-center justify-center h-96 text-center">
        {getFileIcon()}
        <h3 className="mt-4 text-lg font-medium text-gray-900">{file?.displayName || file?.name}</h3>
        <p className="mt-2 text-sm text-gray-500">Preview not supported for this file type</p>
      </div>
    );
  };

  if (!isOpen || !file) return null;

  const modalClasses = fullscreen 
    ? "fixed inset-0 bg-black bg-opacity-95 flex items-center justify-center z-50"
    : "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50";

  const contentClasses = fullscreen
    ? "bg-white rounded-lg shadow-xl w-full h-full mx-4 my-4 flex flex-col"
    : "bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] flex flex-col";

  return (
    <div className={modalClasses}>
      <div className={contentClasses}>
        {/* Header */}
        <div className="flex items-center justify-between p-4 border-b flex-shrink-0">
          <div className="flex items-center gap-3">
            <div>
              <h2 className="text-lg font-semibold text-gray-900">
                {file.displayName || file.name}
              </h2>
              <p className="text-sm text-gray-500">
                {file.mimeType} • {Math.round(file.fileSize / 1024)} KB
              </p>
            </div>
          </div>
          
          {/* Controls */}
          <div className="flex items-center gap-2">
            {previewUrl && ['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'].includes(file.name.split('.').pop()?.toLowerCase() || '') && (
              <>
                <button
                  onClick={handleZoomOut}
                  className="p-2 text-gray-400 hover:text-gray-600 rounded-lg"
                  title="Zoom Out"
                >
                  <MagnifyingGlassMinusIcon className="w-5 h-5" />
                </button>
                <span className="text-sm text-gray-600 min-w-[3rem] text-center">
                  {zoom}%
                </span>
                <button
                  onClick={handleZoomIn}
                  className="p-2 text-gray-400 hover:text-gray-600 rounded-lg"
                  title="Zoom In"
                >
                  <MagnifyingGlassPlusIcon className="w-5 h-5" />
                </button>
                <button
                  onClick={handleZoomReset}
                  className="px-3 py-1 text-sm text-gray-600 hover:text-gray-800 border border-gray-300 rounded"
                  title="Reset Zoom"
                >
                  Reset
                </button>
              </>
            )}
            
            <button
              onClick={toggleFullscreen}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-lg"
              title={fullscreen ? "Exit Fullscreen" : "Fullscreen"}
            >
              <ArrowsPointingOutIcon className="w-5 h-5" />
            </button>
            
            <button
              onClick={handleDownload}
              className="p-2 text-gray-400 hover:text-blue-600 rounded-lg"
              title="Download"
            >
              <ArrowDownTrayIcon className="w-5 h-5" />
            </button>
            
            <button
              onClick={onClose}
              className="p-2 text-gray-400 hover:text-gray-600 rounded-lg"
              title="Close"
            >
              <XMarkIcon className="w-5 h-5" />
            </button>
          </div>
        </div>

        {/* Preview Content */}
        <div className="flex-1 overflow-hidden">
          {renderPreview()}
        </div>
      </div>
    </div>
  );
}
