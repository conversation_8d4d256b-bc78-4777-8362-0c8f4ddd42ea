"use client";

import React, { useState, useEffect } from 'react';
import {
  XMarkIcon,
  DocumentIcon,
  PhotoIcon,
  FilmIcon,
  DocumentTextIcon,
  ArrowDownTrayIcon,
  PencilIcon,
  ShareIcon,
  TrashIcon,
  TagIcon,
  ClockIcon,
  UserIcon,
  EyeIcon,
  ArchiveBoxIcon
} from '@heroicons/react/24/outline';
import { FileDto, FileUpdateData } from '@/api/types/interfaces';
import { FileService } from '@/api/services/fileService';

interface FileDetailModalProps {
  isOpen: boolean;
  onClose: () => void;
  file: FileDto | null;
  fileService: FileService;
  onFileUpdated?: (file: FileDto) => void;
  onFileDeleted?: (fileId: string) => void;
}

export function FileDetailModal({
  isOpen,
  onClose,
  file,
  fileService,
  onFileUpdated,
  onFileDeleted
}: FileDetailModalProps) {
  const [isEditing, setIsEditing] = useState(false);
  const [editData, setEditData] = useState<FileUpdateData>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [tags, setTags] = useState<string[]>([]);
  const [newTag, setNewTag] = useState('');

  useEffect(() => {
    if (file) {
      setEditData({
        displayName: file.displayName,
        description: file.description
      });
      setTags(file.tags || []);
    }
  }, [file]);

  if (!isOpen || !file) return null;

  const formatFileSize = (bytes: number) => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDate = (dateString: string) => {
    return new Intl.DateTimeFormat('vi-VN', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).format(new Date(dateString));
  };

  const getFileIcon = () => {
    const extension = file.name.split('.').pop()?.toLowerCase();
    
    if (['jpg', 'jpeg', 'png', 'gif', 'bmp', 'svg', 'webp'].includes(extension || '')) {
      return <PhotoIcon className="w-8 h-8 text-purple-500" />;
    }
    if (['mp4', 'avi', 'mov', 'wmv', 'flv', 'webm'].includes(extension || '')) {
      return <FilmIcon className="w-8 h-8 text-red-500" />;
    }
    if (['pdf'].includes(extension || '')) {
      return <DocumentTextIcon className="w-8 h-8 text-red-600" />;
    }
    return <DocumentIcon className="w-8 h-8 text-gray-500" />;
  };

  const handleSave = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const updatedFile = await fileService.update(file.id, editData);
      setIsEditing(false);
      onFileUpdated?.(updatedFile);
    } catch (error: any) {
      setError(error.message || 'Failed to update file');
    } finally {
      setLoading(false);
    }
  };

  const handleDownload = async () => {
    try {
      const response = await fileService.download(file.id, { presigned: true });
      
      if ('url' in response) {
        const link = document.createElement('a');
        link.href = response.url;
        link.download = file.displayName || file.name;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      }
    } catch (error: any) {
      setError('Failed to download file');
    }
  };

  const handleDelete = async () => {
    if (!confirm('Are you sure you want to delete this file?')) return;
    
    setLoading(true);
    try {
      await fileService.delete(file.id);
      onFileDeleted?.(file.id);
      onClose();
    } catch (error: any) {
      setError(error.message || 'Failed to delete file');
    } finally {
      setLoading(false);
    }
  };

  const handleAddTag = async () => {
    if (!newTag.trim()) return;
    
    try {
      const updatedFile = await fileService.addTags(file.id, [newTag.trim()]);
      setTags(updatedFile.tags || []);
      setNewTag('');
      onFileUpdated?.(updatedFile);
    } catch (error: any) {
      setError('Failed to add tag');
    }
  };

  const handleRemoveTag = async (tagToRemove: string) => {
    try {
      const updatedFile = await fileService.removeTags(file.id, [tagToRemove]);
      setTags(updatedFile.tags || []);
      onFileUpdated?.(updatedFile);
    } catch (error: any) {
      setError('Failed to remove tag');
    }
  };

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
        {/* Header */}
        <div className="flex items-center justify-between p-6 border-b">
          <div className="flex items-center gap-3">
            {getFileIcon()}
            <div>
              <h2 className="text-xl font-semibold text-gray-900">
                {file.displayName || file.name}
              </h2>
              <p className="text-sm text-gray-500">{file.mimeType}</p>
            </div>
          </div>
          <button
            onClick={onClose}
            className="p-2 text-gray-400 hover:text-gray-600 rounded-lg"
          >
            <XMarkIcon className="w-5 h-5" />
          </button>
        </div>

        {/* Content */}
        <div className="p-6 space-y-6">
          {error && (
            <div className="bg-red-50 border border-red-200 rounded-lg p-4">
              <p className="text-red-800 text-sm">{error}</p>
            </div>
          )}

          {/* File Info */}
          <div className="grid grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                File Name
              </label>
              {isEditing ? (
                <input
                  type="text"
                  value={editData.displayName || ''}
                  onChange={(e) => setEditData(prev => ({ ...prev, displayName: e.target.value }))}
                  className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                />
              ) : (
                <p className="text-gray-900">{file.displayName || file.name}</p>
              )}
            </div>
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Size
              </label>
              <p className="text-gray-900">{formatFileSize(file.fileSize)}</p>
            </div>
          </div>

          {/* Description */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Description
            </label>
            {isEditing ? (
              <textarea
                value={editData.description || ''}
                onChange={(e) => setEditData(prev => ({ ...prev, description: e.target.value }))}
                rows={3}
                className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                placeholder="Add a description..."
              />
            ) : (
              <p className="text-gray-900">{file.description || 'No description'}</p>
            )}
          </div>

          {/* Tags */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Tags
            </label>
            <div className="flex flex-wrap gap-2 mb-2">
              {tags.map((tag) => (
                <span
                  key={tag}
                  className="inline-flex items-center gap-1 px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                >
                  <TagIcon className="w-3 h-3" />
                  {tag}
                  <button
                    onClick={() => handleRemoveTag(tag)}
                    className="text-blue-600 hover:text-blue-800"
                  >
                    <XMarkIcon className="w-3 h-3" />
                  </button>
                </span>
              ))}
            </div>
            <div className="flex gap-2">
              <input
                type="text"
                value={newTag}
                onChange={(e) => setNewTag(e.target.value)}
                placeholder="Add tag..."
                className="flex-1 px-3 py-1 text-sm border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
                onKeyDown={(e) => e.key === 'Enter' && handleAddTag()}
              />
              <button
                onClick={handleAddTag}
                className="px-3 py-1 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700"
              >
                Add
              </button>
            </div>
          </div>

          {/* Metadata */}
          <div className="grid grid-cols-2 gap-4 pt-4 border-t">
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <ClockIcon className="w-4 h-4" />
              <div>
                <p className="font-medium">Created</p>
                <p>{formatDate(file.createdAt)}</p>
              </div>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <ClockIcon className="w-4 h-4" />
              <div>
                <p className="font-medium">Modified</p>
                <p>{formatDate(file.updatedAt)}</p>
              </div>
            </div>
            <div className="flex items-center gap-2 text-sm text-gray-600">
              <UserIcon className="w-4 h-4" />
              <div>
                <p className="font-medium">Owner</p>
                <p>{file.ownerName || 'Unknown'}</p>
              </div>
            </div>
          </div>
        </div>

        {/* Actions */}
        <div className="flex items-center justify-between p-6 border-t bg-gray-50">
          <div className="flex items-center gap-2">
            <button
              onClick={handleDownload}
              className="flex items-center gap-2 px-3 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700"
            >
              <ArrowDownTrayIcon className="w-4 h-4" />
              Download
            </button>
            <button className="flex items-center gap-2 px-3 py-2 text-sm bg-green-600 text-white rounded-lg hover:bg-green-700">
              <ShareIcon className="w-4 h-4" />
              Share
            </button>
            <button className="flex items-center gap-2 px-3 py-2 text-sm bg-gray-600 text-white rounded-lg hover:bg-gray-700">
              <ArchiveBoxIcon className="w-4 h-4" />
              Archive
            </button>
          </div>
          
          <div className="flex items-center gap-2">
            {isEditing ? (
              <>
                <button
                  onClick={() => setIsEditing(false)}
                  className="px-4 py-2 text-sm text-gray-600 hover:text-gray-800"
                  disabled={loading}
                >
                  Cancel
                </button>
                <button
                  onClick={handleSave}
                  disabled={loading}
                  className="px-4 py-2 text-sm bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
                >
                  {loading ? 'Saving...' : 'Save'}
                </button>
              </>
            ) : (
              <>
                <button
                  onClick={() => setIsEditing(true)}
                  className="flex items-center gap-2 px-3 py-2 text-sm text-gray-600 hover:text-gray-800"
                >
                  <PencilIcon className="w-4 h-4" />
                  Edit
                </button>
                <button
                  onClick={handleDelete}
                  disabled={loading}
                  className="flex items-center gap-2 px-3 py-2 text-sm text-red-600 hover:text-red-800"
                >
                  <TrashIcon className="w-4 h-4" />
                  Delete
                </button>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
