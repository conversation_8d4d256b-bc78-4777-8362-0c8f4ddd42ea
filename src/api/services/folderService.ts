import { ApiClient } from '../core/apiClient';
import {
  FolderDto,
  FolderCreateData,
  FolderUpdateData,
  FolderContentsOptions,
  FolderContentsResponse,
  FolderDownloadOptions,
  ShareOptions,
  ShareDto,
  PermissionRequest,
  PermissionDto,
  PaginationInfo,
  SortField,
  SortDirection,
  FolderListApiResponse,
  FolderListOptions
} from '../types/interfaces';

export interface FolderListResponse {
  folders: FolderDto[];
  page: number;
  pageSize: number;
  totalCount: number;
  totalPages: number;
}

export interface FolderMoveOptions {
  targetParentFolderId?: string;
}

export interface BulkDeleteResponse {
  successful: string[];
  failed: { id: string; error: string }[];
}

export interface BulkDeleteOptions {
  folderIds: string[];
  force?: boolean;
  permanent?: boolean;
}

export class FolderService {
  constructor(private apiClient: ApiClient) {}

  /**
   * Create a new folder
   */
  async create(data: FolderCreateData): Promise<FolderDto> {
    try {
      return await this.apiClient.post<FolderDto>('/folders', data);
    } catch (error: any) {
      // Enhanced error handling for folder creation
      if (error.statusCode === 400) {
        if (error.isType?.('DUPLICATE_FOLDER_NAME')) {
          throw new Error(`A folder with the name "${data.name}" already exists in this location`);
        } else if (error.isType?.('INVALID_PARENT_FOLDER')) {
          throw new Error('The specified parent folder does not exist or is inaccessible');
        } else if (error.getValidationErrors?.()) {
          const validationErrors = error.getValidationErrors();
          const errorMessages = Object.entries(validationErrors)
            .map(([field, messages]) => `${field}: ${(messages as string[]).join(', ')}`)
            .join('; ');
          throw new Error(`Validation failed: ${errorMessages}`);
        } else {
          throw new Error(`Invalid folder data: ${error.message}`);
        }
      } else if (error.statusCode === 401) {
        throw new Error('Authentication required to create folders');
      } else if (error.statusCode === 403) {
        throw new Error('Insufficient permissions to create folders in this location');
      } else if (error.statusCode === 409) {
        throw new Error(`Folder name "${data.name}" already exists in this location`);
      }
      throw error;
    }
  }

  /**
   * Get list of user folders with filtering and pagination
   */
  async getList(options: FolderListOptions = {}): Promise<{ items: FolderDto[]; pagination: PaginationInfo }> {
    const params = new URLSearchParams();

    if (options.parentFolderId) params.append('parentFolderId', options.parentFolderId);
    if (options.page) params.append('page', options.page.toString());
    if (options.pageSize) params.append('pageSize', options.pageSize.toString());
    if (options.search) params.append('search', options.search);
    if (options.sortBy) params.append('sortBy', options.sortBy);
    if (options.sortDirection) params.append('sortDirection', options.sortDirection);
    if (options.uploaderEmail) params.append('uploaderEmail', options.uploaderEmail);
    if (options.folderType && options.folderType !== 'all') {
      params.append('folderType', options.folderType);
    }
    if (options.createdAfter) params.append('createdAfter', options.createdAfter);
    if (options.createdBefore) params.append('createdBefore', options.createdBefore);
    if (options.includeShared !== undefined) {
      params.append('includeShared', options.includeShared.toString());
    }
    if (options.isArchived !== undefined) {
      params.append('isArchived', options.isArchived.toString());
    }

    try {
      const apiResponse = await this.apiClient.get<FolderListApiResponse>(`/folders?${params.toString()}`);
      
      // Standardize response format handling
      if ('data' in apiResponse && apiResponse.data) {
        // API response format from documentation
        return {
          items: apiResponse.data.items || [],
          pagination: {
            page: apiResponse.data.page || 1,
            pageSize: apiResponse.data.pageSize || 20,
            totalItems: apiResponse.data.totalCount || 0,
            totalPages: apiResponse.data.totalPages || 0,
            hasNext: apiResponse.data.hasNextPage || false,
            hasPrevious: apiResponse.data.hasPreviousPage || false
          }
        };
      } else {
        // Fallback for legacy format
        const directResponse = apiResponse as any;
        return {
          items: directResponse.items || directResponse || [],
          pagination: directResponse.pagination || {
            page: 1,
            pageSize: 20,
            totalItems: Array.isArray(directResponse.items) ? directResponse.items.length : 0,
            totalPages: 1,
            hasNext: false,
            hasPrevious: false
          }
        };
      }
    } catch (error: any) {
      // Enhanced error handling for specific folder list errors
      if (error.statusCode === 401) {
        throw new Error('Authentication required to access folders');
      } else if (error.statusCode === 403) {
        throw new Error('Insufficient permissions to list folders');
      } else if (error.statusCode === 400) {
        throw new Error(`Invalid request parameters: ${error.message}`);
      }
      throw error;
    }
  }

  /**
   * Get folder details by ID
   */
  async getById(id: string): Promise<FolderDto> {
    try {
      return await this.apiClient.get<FolderDto>(`/folders/${id}`);
    } catch (error: any) {
      // Enhanced error handling for folder retrieval
      if (error.statusCode === 401) {
        throw new Error('Authentication required to access folder details');
      } else if (error.statusCode === 403) {
        throw new Error('Insufficient permissions to access this folder');
      } else if (error.statusCode === 404) {
        throw new Error('Folder not found or has been deleted');
      }
      throw error;
    }
  }

  /**
   * Update folder metadata
   */
  async update(id: string, data: FolderUpdateData): Promise<FolderDto> {
    try {
      return await this.apiClient.put<FolderDto>(`/folders/${id}`, data);
    } catch (error: any) {
      // Enhanced error handling for folder update
      if (error.statusCode === 400) {
        if (error.isType?.('DUPLICATE_FOLDER_NAME')) {
          throw new Error(`A folder with the name "${data.name}" already exists in this location`);
        } else if (error.getValidationErrors?.()) {
          const validationErrors = error.getValidationErrors();
          const errorMessages = Object.entries(validationErrors)
            .map(([field, messages]) => `${field}: ${(messages as string[]).join(', ')}`)
            .join('; ');
          throw new Error(`Update validation failed: ${errorMessages}`);
        } else {
          throw new Error(`Cannot update folder: ${error.message}`);
        }
      } else if (error.statusCode === 401) {
        throw new Error('Authentication required to update folders');
      } else if (error.statusCode === 403) {
        throw new Error('Insufficient permissions to update this folder');
      } else if (error.statusCode === 404) {
        throw new Error('Folder not found or has been deleted');
      } else if (error.statusCode === 409) {
        throw new Error(`Folder name "${data.name}" already exists in this location`);
      }
      throw error;
    }
  }

  /**
   * Delete folder (move to recycle bin by default)
   */
  async delete(id: string, permanent: boolean = false, force: boolean = false): Promise<void> {
    const params = new URLSearchParams();
    if (permanent) params.append('permanent', 'true');
    if (force) params.append('force', 'true');

    try {
      return await this.apiClient.delete<void>(`/folders/${id}?${params.toString()}`);
    } catch (error: any) {
      // Enhanced error handling for folder deletion
      if (error.statusCode === 400) {
        if (error.message?.includes('not empty') && !force) {
          throw new Error('Cannot delete folder: Folder is not empty. Use force=true to delete non-empty folders.');
        } else {
          throw new Error(`Cannot delete folder: ${error.message}`);
        }
      } else if (error.statusCode === 401) {
        throw new Error('Authentication required to delete folders');
      } else if (error.statusCode === 403) {
        throw new Error('Insufficient permissions to delete this folder');
      } else if (error.statusCode === 404) {
        throw new Error('Folder not found or has already been deleted');
      } else if (error.statusCode === 409) {
        throw new Error('Cannot delete folder: It contains items that are currently in use');
      }
      throw error;
    }
  }

  /**
   * Get folder contents (files and subfolders)
   */
  async getContents(id: string, options: FolderContentsOptions = {}): Promise<FolderContentsResponse> {
    const params = new URLSearchParams();

    if (options.page) params.append('page', options.page.toString());
    if (options.pageSize) params.append('pageSize', options.pageSize.toString());
    if (options.sortBy) params.append('sortBy', options.sortBy);
    if (options.sortDirection) params.append('sortDirection', options.sortDirection);

    try {
      return await this.apiClient.get<FolderContentsResponse>(`/folders/${id}/contents?${params.toString()}`);
    } catch (error: any) {
      // Enhanced error handling for folder contents retrieval
      if (error.statusCode === 401) {
        throw new Error('Authentication required to access folder contents');
      } else if (error.statusCode === 403) {
        throw new Error('Insufficient permissions to view the contents of this folder');
      } else if (error.statusCode === 404) {
        throw new Error('Folder not found or has been deleted');
      } else if (error.statusCode === 400) {
        throw new Error(`Invalid request parameters: ${error.message}`);
      }
      throw error;
    }
  }

  /**
   * Move folder to a different parent
   */
  async move(id: string, options: FolderMoveOptions): Promise<FolderDto> {
    try {
      return await this.apiClient.post<FolderDto>(`/folders/${id}/move`, options);
    } catch (error: any) {
      // Enhanced error handling for folder move operation
      if (error.statusCode === 400) {
        if (error.message?.includes('circular') || error.message?.includes('recursive')) {
          throw new Error('Cannot move folder: This would create a circular reference (folder cannot be moved into itself or its subfolders)');
        } else if (error.message?.includes('same location')) {
          throw new Error('Cannot move folder: The folder is already in the specified location');
        } else if (error.getValidationErrors?.()) {
          const validationErrors = error.getValidationErrors();
          const errorMessages = Object.entries(validationErrors)
            .map(([field, messages]) => `${field}: ${(messages as string[]).join(', ')}`)
            .join('; ');
          throw new Error(`Move validation failed: ${errorMessages}`);
        } else {
          throw new Error(`Cannot move folder: ${error.message}`);
        }
      } else if (error.statusCode === 401) {
        throw new Error('Authentication required to move folders');
      } else if (error.statusCode === 403) {
        throw new Error('Insufficient permissions to move this folder or access the target location');
      } else if (error.statusCode === 404) {
        throw new Error('Folder not found or target parent folder does not exist');
      } else if (error.statusCode === 409) {
        throw new Error('Cannot move folder: A folder with the same name already exists in the target location');
      }
      throw error;
    }
  }

  /**
   * Download folder as ZIP file
   */
  async download(id: string, options: FolderDownloadOptions = {}): Promise<Blob> {
    const params = new URLSearchParams();
    
    if (options.includeSubfolders !== undefined) {
      params.append('includeSubfolders', options.includeSubfolders.toString());
    }
    if (options.maxZipSize) {
      params.append('maxZipSize', options.maxZipSize.toString());
    }

    return this.apiClient.downloadFile(`/folders/${id}/download?${params.toString()}`);
  }

  /**
   * Bulk delete folders
   */
  async bulkDelete(ids: string[], permanent: boolean = false, force: boolean = false): Promise<BulkDeleteResponse> {
    const options: BulkDeleteOptions = {
      folderIds: ids,
      permanent,
      force
    };

    return this.apiClient.delete<BulkDeleteResponse>('/folders/bulk', { data: options });
  }

  /**
   * Create a shareable link for the folder
   */
  async createShare(id: string, options: ShareOptions & { includeSubfolders?: boolean }): Promise<ShareDto> {
    try {
      return await this.apiClient.post<ShareDto>(`/folders/${id}/shares`, options);
    } catch (error: any) {
      // Enhanced error handling for folder sharing
      if (error.statusCode === 400) {
        if (error.isType?.('SHARE_LIMIT_EXCEEDED')) {
          throw new Error('Cannot create share: Maximum number of shares for this folder has been reached');
        } else if (error.isType?.('INVALID_SHARE_PASSWORD')) {
          throw new Error('Cannot create share: Password does not meet security requirements');
        } else if (error.getValidationErrors?.()) {
          const validationErrors = error.getValidationErrors();
          const errorMessages = Object.entries(validationErrors)
            .map(([field, messages]) => `${field}: ${(messages as string[]).join(', ')}`)
            .join('; ');
          throw new Error(`Share validation failed: ${errorMessages}`);
        } else {
          throw new Error(`Cannot create share: ${error.message}`);
        }
      } else if (error.statusCode === 401) {
        throw new Error('Authentication required to create folder shares');
      } else if (error.statusCode === 403) {
        throw new Error('Insufficient permissions to share this folder');
      } else if (error.statusCode === 404) {
        throw new Error('Folder not found or has been deleted');
      }
      throw error;
    }
  }

  /**
   * Get all shares for a folder
   */
  async getShares(id: string): Promise<ShareDto[]> {
    return this.apiClient.get<ShareDto[]>(`/folders/${id}/shares`);
  }

  /**
   * Delete a folder share
   */
  async deleteShare(folderId: string, shareId: string): Promise<void> {
    return this.apiClient.delete<void>(`/folders/${folderId}/shares/${shareId}`);
  }

  /**
   * Grant permission to a user or role for the folder
   */
  async grantPermission(id: string, request: PermissionRequest & { inheritToChildren?: boolean }): Promise<string> {
    const response = await this.apiClient.post<{ id: string }>(`/folders/${id}/permissions`, request);
    return response.id;
  }

  /**
   * Get all permissions for a folder
   */
  async getPermissions(id: string): Promise<PermissionDto[]> {
    return this.apiClient.get<PermissionDto[]>(`/folders/${id}/permissions`);
  }

  /**
   * Remove permission from a folder
   */
  async removePermission(folderId: string, permissionId: string): Promise<void> {
    return this.apiClient.delete<void>(`/folders/${folderId}/permissions/${permissionId}`);
  }

  /**
   * Get folder size and usage statistics
   */
  async getStatistics(id: string): Promise<{
    totalSize: number;
    fileCount: number;
    folderCount: number;
    lastModified: string;
    sizeBreakdown: {
      documents: number;
      images: number;
      videos: number;
      others: number;
    };
  }> {
    try {
      return await this.apiClient.get<any>(`/folders/${id}/statistics`);
    } catch (error: any) {
      if (error.statusCode === 404) {
        throw new Error('Folder not found or has been deleted');
      } else if (error.statusCode === 403) {
        throw new Error('Insufficient permissions to view folder statistics');
      }
      throw error;
    }
  }

  /**
   * Advanced folder search with filters
   */
  async search(options: {
    query?: string;
    tags?: string[];
    createdAfter?: string;
    createdBefore?: string;
    modifiedAfter?: string;
    modifiedBefore?: string;
    minSize?: number;
    maxSize?: number;
    ownerIds?: string[];
    includeSubfolders?: boolean;
    page?: number;
    pageSize?: number;
  }): Promise<{ items: FolderDto[]; pagination: PaginationInfo }> {
    const params = new URLSearchParams();
    
    if (options.query) params.append('query', options.query);
    if (options.tags?.length) params.append('tags', options.tags.join(','));
    if (options.createdAfter) params.append('createdAfter', options.createdAfter);
    if (options.createdBefore) params.append('createdBefore', options.createdBefore);
    if (options.modifiedAfter) params.append('modifiedAfter', options.modifiedAfter);
    if (options.modifiedBefore) params.append('modifiedBefore', options.modifiedBefore);
    if (options.minSize) params.append('minSize', options.minSize.toString());
    if (options.maxSize) params.append('maxSize', options.maxSize.toString());
    if (options.ownerIds?.length) params.append('ownerIds', options.ownerIds.join(','));
    if (options.includeSubfolders) params.append('includeSubfolders', 'true');
    if (options.page) params.append('page', options.page.toString());
    if (options.pageSize) params.append('pageSize', options.pageSize.toString());

    try {
      return await this.apiClient.get<{ items: FolderDto[]; pagination: PaginationInfo }>(`/folders/search?${params.toString()}`);
    } catch (error: any) {
      if (error.statusCode === 400) {
        throw new Error('Invalid search parameters provided');
      }
      throw error;
    }
  }

  /**
   * Get deleted folders in recycle bin
   */
  async getRecycleBin(options: {
    page?: number;
    pageSize?: number;
    sortBy?: SortField;
    sortDirection?: SortDirection;
  } = {}): Promise<{ items: FolderDto[]; pagination: PaginationInfo }> {
    const params = new URLSearchParams();
    
    if (options.page) params.append('page', options.page.toString());
    if (options.pageSize) params.append('pageSize', options.pageSize.toString());
    if (options.sortBy) params.append('sortBy', options.sortBy);
    if (options.sortDirection) params.append('sortDirection', options.sortDirection);

    try {
      return await this.apiClient.get<{ items: FolderDto[]; pagination: PaginationInfo }>(`/folders/recycle-bin?${params.toString()}`);
    } catch (error: any) {
      if (error.statusCode === 403) {
        throw new Error('Insufficient permissions to view recycle bin');
      }
      throw error;
    }
  }

  /**
   * Restore folder from recycle bin
   */
  async restore(id: string, options?: { newParentFolderId?: string; newName?: string }): Promise<FolderDto> {
    try {
      return await this.apiClient.post<FolderDto>(`/folders/${id}/restore`, options || {});
    } catch (error: any) {
      if (error.statusCode === 404) {
        throw new Error('Folder not found in recycle bin');
      } else if (error.statusCode === 400) {
        if (error.message?.includes('name conflict')) {
          throw new Error('A folder with this name already exists in the target location');
        } else if (error.message?.includes('parent folder')) {
          throw new Error('The specified parent folder does not exist or is inaccessible');
        }
      } else if (error.statusCode === 403) {
        throw new Error('Insufficient permissions to restore this folder');
      }
      throw error;
    }
  }

  /**
   * Permanently delete folder from recycle bin
   */
  async permanentDelete(id: string): Promise<void> {
    try {
      return await this.apiClient.delete<void>(`/folders/${id}/permanent`);
    } catch (error: any) {
      if (error.statusCode === 404) {
        throw new Error('Folder not found in recycle bin');
      } else if (error.statusCode === 403) {
        throw new Error('Insufficient permissions to permanently delete this folder');
      }
      throw error;
    }
  }

  /**
   * Sync folder to Google Drive
   */
  async syncToGoogleDrive(id: string, options?: { 
    targetFolderId?: string; 
    syncSubfolders?: boolean;
    conflictResolution?: 'skip' | 'overwrite' | 'rename';
  }): Promise<{ 
    jobId: string; 
    status: 'started' | 'in_progress' | 'completed' | 'failed';
    message?: string;
  }> {
    try {
      return await this.apiClient.post<any>(`/folders/${id}/sync-google-drive`, options || {});
    } catch (error: any) {
      if (error.statusCode === 400) {
        if (error.message?.includes('google drive not configured')) {
          throw new Error('Google Drive integration is not configured for this account');
        } else if (error.message?.includes('quota exceeded')) {
          throw new Error('Google Drive storage quota exceeded');
        }
      } else if (error.statusCode === 403) {
        throw new Error('Insufficient permissions to sync to Google Drive');
      }
      throw error;
    }
  }

  /**
   * Get sync status with external services
   */
  async getSyncStatus(id: string): Promise<{
    googleDrive?: {
      status: 'not_synced' | 'syncing' | 'synced' | 'error';
      lastSync?: string;
      folderId?: string;
      errorMessage?: string;
    };
  }> {
    try {
      return await this.apiClient.get<any>(`/folders/${id}/sync-status`);
    } catch (error: any) {
      if (error.statusCode === 404) {
        throw new Error('Folder not found or has been deleted');
      }
      throw error;
    }
  }

  /**
   * Export folder structure metadata
   */
  async exportStructure(options?: {
    format?: 'json' | 'csv' | 'xml';
    includeFiles?: boolean;
    includePermissions?: boolean;
    parentFolderId?: string;
  }): Promise<{ downloadUrl: string; expiresAt: string }> {
    const params = new URLSearchParams();
    
    if (options?.format) params.append('format', options.format);
    if (options?.includeFiles) params.append('includeFiles', 'true');
    if (options?.includePermissions) params.append('includePermissions', 'true');
    if (options?.parentFolderId) params.append('parentFolderId', options.parentFolderId);

    try {
      return await this.apiClient.get<{ downloadUrl: string; expiresAt: string }>(`/folders/export?${params.toString()}`);
    } catch (error: any) {
      if (error.statusCode === 403) {
        throw new Error('Insufficient permissions to export folder structure');
      }
      throw error;
    }
  }

  /**
   * Import folder structure
   */
  async importStructure(file: File, options?: {
    format?: 'json' | 'csv' | 'xml';
    parentFolderId?: string;
    conflictResolution?: 'skip' | 'overwrite' | 'rename';
  }): Promise<{ 
    jobId: string;
    status: 'started' | 'in_progress' | 'completed' | 'failed';
    createdFolders?: number;
    skippedFolders?: number;
  }> {
    const formData = new FormData();
    formData.append('file', file);
    
    if (options?.format) formData.append('format', options.format);
    if (options?.parentFolderId) formData.append('parentFolderId', options.parentFolderId);
    if (options?.conflictResolution) formData.append('conflictResolution', options.conflictResolution);

    try {
      return await this.apiClient.post<any>('/folders/import', formData);
    } catch (error: any) {
      if (error.statusCode === 400) {
        if (error.message?.includes('invalid file format')) {
          throw new Error('Invalid file format. Please use JSON, CSV, or XML format');
        } else if (error.message?.includes('file too large')) {
          throw new Error('Import file is too large. Maximum size is 10MB');
        }
      } else if (error.statusCode === 403) {
        throw new Error('Insufficient permissions to import folder structure');
      }
      throw error;
    }
  }

  /**
   * Get folder activity history
   */
  async getAuditLogs(id: string, options?: {
    startDate?: string;
    endDate?: string;
    actions?: string[];
    userIds?: string[];
    page?: number;
    pageSize?: number;
  }): Promise<{
    items: Array<{
      id: string;
      action: string;
      userId: string;
      userName?: string;
      timestamp: string;
      details: any;
      ipAddress?: string;
      userAgent?: string;
    }>;
    pagination: PaginationInfo;
  }> {
    const params = new URLSearchParams();
    
    if (options?.startDate) params.append('startDate', options.startDate);
    if (options?.endDate) params.append('endDate', options.endDate);
    if (options?.actions?.length) params.append('actions', options.actions.join(','));
    if (options?.userIds?.length) params.append('userIds', options.userIds.join(','));
    if (options?.page) params.append('page', options.page.toString());
    if (options?.pageSize) params.append('pageSize', options.pageSize.toString());

    try {
      return await this.apiClient.get<any>(`/folders/${id}/audit-logs?${params.toString()}`);
    } catch (error: any) {
      if (error.statusCode === 404) {
        throw new Error('Folder not found or has been deleted');
      } else if (error.statusCode === 403) {
        throw new Error('Insufficient permissions to view audit logs');
      }
      throw error;
    }
  }
}